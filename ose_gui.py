#!/usr/bin/env python3

"""
OneShot-Extended GUI
A graphical user interface for the OneShot-Extended WPS penetration testing utility.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import subprocess
import threading
import os
import sys
from pathlib import Path

class OneShotGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("OneShot-Extended GUI")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        # Variables for form fields
        self.interface_var = tk.StringVar()
        self.bssid_var = tk.StringVar()
        self.pin_var = tk.StringVar()
        self.delay_var = tk.StringVar()
        self.vuln_list_var = tk.StringVar(value="vulnwsc.txt")
        
        # Boolean variables for checkboxes
        self.pixie_dust_var = tk.BooleanVar()
        self.pixie_force_var = tk.BooleanVar()
        self.show_pixie_cmd_var = tk.BooleanVar()
        self.bruteforce_var = tk.BooleanVar()
        self.pbc_var = tk.BooleanVar()
        self.write_var = tk.BooleanVar()
        self.save_var = tk.BooleanVar()
        self.iface_down_var = tk.BooleanVar()
        self.loop_var = tk.BooleanVar()
        self.clear_var = tk.BooleanVar()
        self.reverse_scan_var = tk.BooleanVar()
        self.mtk_wifi_var = tk.BooleanVar()
        self.dts_var = tk.BooleanVar()
        self.verbose_var = tk.BooleanVar()
        
        self.process = None
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface"""
        
        # Create main frame with scrollbar
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create notebook for tabs
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # Basic Settings Tab
        basic_frame = ttk.Frame(notebook)
        notebook.add(basic_frame, text="Basic Settings")
        self.setup_basic_tab(basic_frame)
        
        # Advanced Settings Tab
        advanced_frame = ttk.Frame(notebook)
        notebook.add(advanced_frame, text="Advanced Settings")
        self.setup_advanced_tab(advanced_frame)
        
        # Output Tab
        output_frame = ttk.Frame(notebook)
        notebook.add(output_frame, text="Output")
        self.setup_output_tab(output_frame)
        
    def setup_basic_tab(self, parent):
        """Setup basic settings tab"""
        
        # Required settings
        req_frame = ttk.LabelFrame(parent, text="Required Settings", padding=10)
        req_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(req_frame, text="Interface:").grid(row=0, column=0, sticky=tk.W, pady=2)
        interface_entry = ttk.Entry(req_frame, textvariable=self.interface_var, width=20)
        interface_entry.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        ttk.Button(req_frame, text="Detect", command=self.detect_interfaces).grid(row=0, column=2, padx=(5, 0), pady=2)
        
        # Target settings
        target_frame = ttk.LabelFrame(parent, text="Target Settings", padding=10)
        target_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(target_frame, text="BSSID:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(target_frame, textvariable=self.bssid_var, width=20).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        ttk.Label(target_frame, text="(Leave empty to scan)").grid(row=0, column=2, sticky=tk.W, padx=(10, 0), pady=2)
        
        ttk.Label(target_frame, text="PIN:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(target_frame, textvariable=self.pin_var, width=20).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        ttk.Label(target_frame, text="(Optional: 4/8 digit PIN)").grid(row=1, column=2, sticky=tk.W, padx=(10, 0), pady=2)
        
        # Attack methods
        attack_frame = ttk.LabelFrame(parent, text="Attack Methods", padding=10)
        attack_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Checkbutton(attack_frame, text="Pixie Dust Attack", variable=self.pixie_dust_var).pack(anchor=tk.W, pady=2)
        ttk.Checkbutton(attack_frame, text="Bruteforce Attack", variable=self.bruteforce_var).pack(anchor=tk.W, pady=2)
        ttk.Checkbutton(attack_frame, text="Push Button Connect (PBC)", variable=self.pbc_var).pack(anchor=tk.W, pady=2)
        
        # Pixie Dust options
        pixie_frame = ttk.LabelFrame(parent, text="Pixie Dust Options", padding=10)
        pixie_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Checkbutton(pixie_frame, text="Force full range bruteforce", variable=self.pixie_force_var).pack(anchor=tk.W, pady=2)
        ttk.Checkbutton(pixie_frame, text="Show Pixiewps command", variable=self.show_pixie_cmd_var).pack(anchor=tk.W, pady=2)
        
        # Bruteforce options
        brute_frame = ttk.LabelFrame(parent, text="Bruteforce Options", padding=10)
        brute_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(brute_frame, text="Delay between attempts (seconds):").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(brute_frame, textvariable=self.delay_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
    def setup_advanced_tab(self, parent):
        """Setup advanced settings tab"""
        
        # Output options
        output_frame = ttk.LabelFrame(parent, text="Output Options", padding=10)
        output_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Checkbutton(output_frame, text="Write credentials to file on success", variable=self.write_var).pack(anchor=tk.W, pady=2)
        ttk.Checkbutton(output_frame, text="Save AP to network manager on success", variable=self.save_var).pack(anchor=tk.W, pady=2)
        ttk.Checkbutton(output_frame, text="Verbose output", variable=self.verbose_var).pack(anchor=tk.W, pady=2)
        
        # Interface options
        iface_frame = ttk.LabelFrame(parent, text="Interface Options", padding=10)
        iface_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Checkbutton(iface_frame, text="Down interface when finished", variable=self.iface_down_var).pack(anchor=tk.W, pady=2)
        ttk.Checkbutton(iface_frame, text="MediaTek Wi-Fi driver support", variable=self.mtk_wifi_var).pack(anchor=tk.W, pady=2)
        ttk.Checkbutton(iface_frame, text="Don't touch Android Wi-Fi settings", variable=self.dts_var).pack(anchor=tk.W, pady=2)
        
        # Scan options
        scan_frame = ttk.LabelFrame(parent, text="Scan Options", padding=10)
        scan_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Checkbutton(scan_frame, text="Run in loop mode", variable=self.loop_var).pack(anchor=tk.W, pady=2)
        ttk.Checkbutton(scan_frame, text="Clear screen on every scan", variable=self.clear_var).pack(anchor=tk.W, pady=2)
        ttk.Checkbutton(scan_frame, text="Reverse network list order", variable=self.reverse_scan_var).pack(anchor=tk.W, pady=2)
        
        # Vulnerable devices list
        vuln_frame = ttk.LabelFrame(parent, text="Vulnerable Devices List", padding=10)
        vuln_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(vuln_frame, text="Vulnerable devices file:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(vuln_frame, textvariable=self.vuln_list_var, width=40).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        ttk.Button(vuln_frame, text="Browse", command=self.browse_vuln_list).grid(row=0, column=2, padx=(5, 0), pady=2)
        
        # Control buttons
        control_frame = ttk.Frame(parent)
        control_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Button(control_frame, text="Start Attack", command=self.start_attack, style="Accent.TButton").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="Stop Attack", command=self.stop_attack).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="Clear Output", command=self.clear_output).pack(side=tk.LEFT)
        
    def setup_output_tab(self, parent):
        """Setup output tab"""
        
        # Output text area
        self.output_text = scrolledtext.ScrolledText(parent, wrap=tk.WORD, height=30)
        self.output_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(parent, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        
    def detect_interfaces(self):
        """Detect available network interfaces"""
        try:
            result = subprocess.run(['ip', 'link', 'show'], capture_output=True, text=True)
            interfaces = []
            for line in result.stdout.split('\n'):
                if ': ' in line and 'wl' in line:
                    interface = line.split(': ')[1].split('@')[0]
                    interfaces.append(interface)
            
            if interfaces:
                # Show selection dialog
                interface = self.show_interface_selection(interfaces)
                if interface:
                    self.interface_var.set(interface)
            else:
                messagebox.showwarning("No Interfaces", "No wireless interfaces detected.")
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to detect interfaces: {e}")
    
    def show_interface_selection(self, interfaces):
        """Show interface selection dialog"""
        dialog = tk.Toplevel(self.root)
        dialog.title("Select Interface")
        dialog.geometry("300x200")
        dialog.transient(self.root)
        dialog.grab_set()
        
        selected_interface = tk.StringVar()
        
        ttk.Label(dialog, text="Select a wireless interface:").pack(pady=10)
        
        for interface in interfaces:
            ttk.Radiobutton(dialog, text=interface, variable=selected_interface, value=interface).pack(anchor=tk.W, padx=20)
        
        def on_select():
            dialog.destroy()
        
        ttk.Button(dialog, text="Select", command=on_select).pack(pady=10)
        
        dialog.wait_window()
        return selected_interface.get()
    
    def browse_vuln_list(self):
        """Browse for vulnerable devices list file"""
        filename = filedialog.askopenfilename(
            title="Select Vulnerable Devices List",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.vuln_list_var.set(filename)
    
    def build_command(self):
        """Build the command line arguments"""
        if not self.interface_var.get():
            raise ValueError("Interface is required")
        
        cmd = ['python3', 'ose.py', '-i', self.interface_var.get()]
        
        if self.bssid_var.get():
            cmd.extend(['-b', self.bssid_var.get()])
        
        if self.pin_var.get():
            cmd.extend(['-p', self.pin_var.get()])
        
        if self.pixie_dust_var.get():
            cmd.append('-K')
        
        if self.pixie_force_var.get():
            cmd.append('-F')
        
        if self.show_pixie_cmd_var.get():
            cmd.append('-X')
        
        if self.bruteforce_var.get():
            cmd.append('-B')
        
        if self.pbc_var.get():
            cmd.append('--pbc')
        
        if self.delay_var.get():
            try:
                float(self.delay_var.get())
                cmd.extend(['-d', self.delay_var.get()])
            except ValueError:
                raise ValueError("Delay must be a number")
        
        if self.write_var.get():
            cmd.append('-w')
        
        if self.save_var.get():
            cmd.append('-s')
        
        if self.iface_down_var.get():
            cmd.append('--iface-down')
        
        if self.vuln_list_var.get() and self.vuln_list_var.get() != "vulnwsc.txt":
            cmd.extend(['--vuln-list', self.vuln_list_var.get()])
        
        if self.loop_var.get():
            cmd.append('-l')
        
        if self.clear_var.get():
            cmd.append('-c')
        
        if self.reverse_scan_var.get():
            cmd.append('-r')
        
        if self.mtk_wifi_var.get():
            cmd.append('--mtk-wifi')
        
        if self.dts_var.get():
            cmd.append('--dts')
        
        if self.verbose_var.get():
            cmd.append('-v')
        
        return cmd
    
    def start_attack(self):
        """Start the OneShot-Extended attack"""
        try:
            cmd = self.build_command()
            self.log_output(f"Starting command: {' '.join(cmd)}\n")
            self.status_var.set("Running...")
            
            # Start process in a separate thread
            self.attack_thread = threading.Thread(target=self.run_attack, args=(cmd,))
            self.attack_thread.daemon = True
            self.attack_thread.start()
            
        except ValueError as e:
            messagebox.showerror("Error", str(e))
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start attack: {e}")
    
    def run_attack(self, cmd):
        """Run the attack in a separate thread"""
        try:
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # Read output line by line
            for line in iter(self.process.stdout.readline, ''):
                if line:
                    self.root.after(0, self.log_output, line)
            
            self.process.wait()
            self.root.after(0, self.on_attack_finished)
            
        except Exception as e:
            self.root.after(0, self.log_output, f"Error: {e}\n")
            self.root.after(0, self.on_attack_finished)
    
    def stop_attack(self):
        """Stop the running attack"""
        if self.process and self.process.poll() is None:
            self.process.terminate()
            self.log_output("Attack stopped by user.\n")
            self.status_var.set("Stopped")
    
    def on_attack_finished(self):
        """Called when attack finishes"""
        self.status_var.set("Finished")
        self.process = None
    
    def log_output(self, text):
        """Log output to the text area"""
        self.output_text.insert(tk.END, text)
        self.output_text.see(tk.END)
    
    def clear_output(self):
        """Clear the output text area"""
        self.output_text.delete(1.0, tk.END)

def main():
    """Main function"""
    # Check if we're in the right directory
    if not os.path.exists('ose.py'):
        messagebox.showerror("Error", "ose.py not found in current directory. Please run this GUI from the OneShot-Extended directory.")
        return
    
    root = tk.Tk()
    app = OneShotGUI(root)
    root.mainloop()

if __name__ == '__main__':
    main()
